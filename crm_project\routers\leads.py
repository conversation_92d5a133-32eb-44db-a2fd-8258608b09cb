from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from crm_project.database import get_session
from crm_project.crud import create_lead, get_leads, get_lead
from crm_project.schemas import LeadCreate, LeadRead

router = APIRouter(prefix="/leads", tags=["Leads"])

@router.post("/", response_model=LeadRead)
def add_lead(payload: LeadCreate, db: Session = Depends(get_session)):
    return create_lead(db, payload)

@router.get("/", response_model=list[LeadRead])
def list_leads(db: Session = Depends(get_session)):
    return get_leads(db)

@router.get("/{lead_id}", response_model=LeadRead)
def get_one(lead_id: int, db: Session = Depends(get_session)):
    lead = get_lead(db, lead_id)
    if not lead:
        raise HTTPException(status_code=404, detail="Lead not found")
    return lead
