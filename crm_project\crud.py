from sqlalchemy.orm import Session, joinedload
from sqlalchemy.exc import NoResultFound
from crm_project.models import Contact, Account, Lead, Task, Webhook, WebhookLog, ContactNote, ContactActivity
from crm_project.schemas import (
    Contact<PERSON>reate, ContactUpdate, ContactNoteCreate, Account<PERSON><PERSON>,
    LeadC<PERSON>, TaskCreate, WebhookCreate, WebhookUpdate
)
from typing import List, Optional
from datetime import datetime

# Contacts

def create_contact(db: Session, data: ContactCreate) -> Contact:
    contact_data = data.dict()
    obj = Contact(**contact_data)
    db.add(obj)
    db.commit()
    db.refresh(obj)

    # Create activity record
    create_contact_activity(
        db, obj.id, "contact.created",
        f"Contact {obj.first_name} {obj.last_name} was created"
    )

    return obj

def get_contacts(db: Session) -> List[Contact]:
    return db.query(Contact).options(joinedload(Contact.account)).all()

def get_contact(db: Session, contact_id: int) -> Optional[Contact]:
    return db.query(Contact).options(
        joinedload(Contact.account),
        joinedload(Contact.notes),
        joinedload(Contact.activities)
    ).get(contact_id)

def update_contact(db: Session, contact_id: int, data: ContactUpdate) -> Optional[Contact]:
    contact = db.query(Contact).get(contact_id)
    if not contact:
        return None

    update_data = data.dict(exclude_unset=True)
    old_values = {}

    for field, value in update_data.items():
        if hasattr(contact, field):
            old_values[field] = getattr(contact, field)
            setattr(contact, field, value)

    contact.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(contact)

    # Create activity record
    changes = [f"{k}: {old_values[k]} → {v}" for k, v in update_data.items()]
    create_contact_activity(
        db, contact.id, "contact.updated",
        f"Contact updated: {', '.join(changes)}"
    )

    return contact

def delete_contact(db: Session, contact_id: int) -> bool:
    contact = db.query(Contact).get(contact_id)
    if not contact:
        return False
    db.delete(contact)
    db.commit()
    return True

# Contact Notes

def create_contact_note(db: Session, data: ContactNoteCreate) -> ContactNote:
    obj = ContactNote(**data.dict())
    db.add(obj)
    db.commit()
    db.refresh(obj)

    # Create activity record
    create_contact_activity(
        db, obj.contact_id, "note.created",
        f"Note added: {obj.content[:50]}{'...' if len(obj.content) > 50 else ''}"
    )

    return obj

def get_contact_notes(db: Session, contact_id: int) -> List[ContactNote]:
    return db.query(ContactNote).filter(ContactNote.contact_id == contact_id).order_by(ContactNote.created_at.desc()).all()

def delete_contact_note(db: Session, note_id: int) -> bool:
    note = db.query(ContactNote).get(note_id)
    if not note:
        return False

    contact_id = note.contact_id
    db.delete(note)
    db.commit()

    # Create activity record
    create_contact_activity(
        db, contact_id, "note.deleted",
        "Note was deleted"
    )

    return True

# Contact Activities

def create_contact_activity(db: Session, contact_id: int, activity_type: str, description: str, activity_data: dict = None) -> ContactActivity:
    obj = ContactActivity(
        contact_id=contact_id,
        activity_type=activity_type,
        description=description,
        activity_data=activity_data or {}
    )
    db.add(obj)
    db.commit()
    db.refresh(obj)
    return obj

def get_contact_activities(db: Session, contact_id: int, limit: int = 50) -> List[ContactActivity]:
    return db.query(ContactActivity).filter(
        ContactActivity.contact_id == contact_id
    ).order_by(ContactActivity.created_at.desc()).limit(limit).all()

# Accounts

def create_account(db: Session, data: AccountCreate) -> Account:
    obj = Account(**data.dict())
    db.add(obj)
    db.commit()
    db.refresh(obj)
    return obj

def get_accounts(db: Session) -> List[Account]:
    return db.query(Account).all()

def get_account(db: Session, account_id: int) -> Optional[Account]:
    return db.query(Account).get(account_id)

# Leads

def create_lead(db: Session, data: LeadCreate) -> Lead:
    obj = Lead(**data.dict())
    db.add(obj)
    db.commit()
    db.refresh(obj)
    return obj

def get_leads(db: Session) -> List[Lead]:
    return db.query(Lead).all()

def get_lead(db: Session, lead_id: int) -> Optional[Lead]:
    return db.query(Lead).get(lead_id)

# Tasks

def create_task(db: Session, data: TaskCreate) -> Task:
    obj = Task(**data.dict())
    db.add(obj)
    db.commit()
    db.refresh(obj)
    return obj

def get_tasks(db: Session) -> List[Task]:
    return db.query(Task).all()

def update_task(db: Session, task_id: int) -> Optional[Task]:
    task = db.query(Task).get(task_id)
    if not task:
        return None
    task.completed = True
    db.commit()
    db.refresh(task)
    return task

def delete_task(db: Session, task_id: int) -> bool:
    task = db.query(Task).get(task_id)
    if not task:
        return False
    db.delete(task)
    db.commit()
    return True

# Webhooks

def create_webhook(db: Session, data: WebhookCreate) -> Webhook:
    obj = Webhook(**data.dict())
    db.add(obj)
    db.commit()
    db.refresh(obj)
    return obj

def get_webhooks(db: Session) -> List[Webhook]:
    return db.query(Webhook).all()

def get_webhook(db: Session, webhook_id: int) -> Optional[Webhook]:
    return db.query(Webhook).get(webhook_id)

def update_webhook(db: Session, webhook_id: int, data: WebhookUpdate) -> Optional[Webhook]:
    webhook = db.query(Webhook).get(webhook_id)
    if not webhook:
        return None

    update_data = data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(webhook, field, value)

    db.commit()
    db.refresh(webhook)
    return webhook

def delete_webhook(db: Session, webhook_id: int) -> bool:
    webhook = db.query(Webhook).get(webhook_id)
    if not webhook:
        return False
    db.delete(webhook)
    db.commit()
    return True

def create_webhook_log(db: Session, webhook_id: int, event_type: str, payload: dict,
                      response_status: Optional[int] = None, response_body: Optional[str] = None) -> WebhookLog:
    log = WebhookLog(
        webhook_id=webhook_id,
        event_type=event_type,
        payload=payload,
        response_status=response_status,
        response_body=response_body
    )
    db.add(log)
    db.commit()
    db.refresh(log)
    return log

def get_webhook_logs(db: Session, webhook_id: Optional[int] = None) -> List[WebhookLog]:
    query = db.query(WebhookLog)
    if webhook_id:
        query = query.filter(WebhookLog.webhook_id == webhook_id)
    return query.order_by(WebhookLog.created_at.desc()).all()
