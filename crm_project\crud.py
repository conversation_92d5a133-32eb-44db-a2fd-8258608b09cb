from sqlalchemy.orm import Session
from sqlalchemy.exc import NoResultFound
from crm_project.models import Contact, Account, Lead, Task
from crm_project.schemas import ContactCreate, AccountCreate, LeadCreate, TaskCreate
from typing import List, Optional

# Contacts

def create_contact(db: Session, data: ContactCreate) -> Contact:
    obj = Contact(**data.dict())
    db.add(obj)
    db.commit()
    db.refresh(obj)
    return obj

def get_contacts(db: Session) -> List[Contact]:
    return db.query(Contact).all()

def get_contact(db: Session, contact_id: int) -> Optional[Contact]:
    return db.query(Contact).get(contact_id)

# Accounts

def create_account(db: Session, data: AccountCreate) -> Account:
    obj = Account(**data.dict())
    db.add(obj)
    db.commit()
    db.refresh(obj)
    return obj

def get_accounts(db: Session) -> List[Account]:
    return db.query(Account).all()

def get_account(db: Session, account_id: int) -> Optional[Account]:
    return db.query(Account).get(account_id)

# Leads

def create_lead(db: Session, data: LeadCreate) -> Lead:
    obj = Lead(**data.dict())
    db.add(obj)
    db.commit()
    db.refresh(obj)
    return obj

def get_leads(db: Session) -> List[Lead]:
    return db.query(Lead).all()

def get_lead(db: Session, lead_id: int) -> Optional[Lead]:
    return db.query(Lead).get(lead_id)

# Tasks

def create_task(db: Session, data: TaskCreate) -> Task:
    obj = Task(**data.dict())
    db.add(obj)
    db.commit()
    db.refresh(obj)
    return obj

def get_tasks(db: Session) -> List[Task]:
    return db.query(Task).all()

def update_task(db: Session, task_id: int) -> Optional[Task]:
    task = db.query(Task).get(task_id)
    if not task:
        return None
    task.completed = True
    db.commit()
    db.refresh(task)
    return task

def delete_task(db: Session, task_id: int) -> bool:
    task = db.query(Task).get(task_id)
    if not task:
        return False
    db.delete(task)
    db.commit()
    return True
