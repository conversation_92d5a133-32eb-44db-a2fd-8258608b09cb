from sqlalchemy.orm import Session, joinedload
from sqlalchemy.exc import NoResultFound
from crm_project.models import Contact, Account, Lead, Task, Webhook, WebhookLog, ContactNote, ContactActivity, LeadNote, LeadActivity
from crm_project.schemas import (
    ContactCreate, ContactUpdate, ContactNoteCreate, AccountCreate,
    LeadCreate, LeadUpdate, LeadNoteCreate, TaskCreate, WebhookCreate, WebhookUpdate
)
from typing import List, Optional, Dict, Any
from datetime import datetime

# Contacts

def create_contact(db: Session, data: ContactCreate) -> Contact:
    contact_data = data.dict()
    obj = Contact(**contact_data)
    db.add(obj)
    db.commit()
    db.refresh(obj)

    # Create activity record
    create_contact_activity(
        db, obj.id, "contact.created",
        f"Contact {obj.first_name} {obj.last_name} was created"
    )

    return obj

def get_contacts(db: Session) -> List[Contact]:
    return db.query(Contact).options(joinedload(Contact.account)).all()

def get_contact(db: Session, contact_id: int) -> Optional[Contact]:
    return db.query(Contact).options(
        joinedload(Contact.account),
        joinedload(Contact.notes),
        joinedload(Contact.activities)
    ).get(contact_id)

def update_contact(db: Session, contact_id: int, data: ContactUpdate) -> Optional[Contact]:
    contact = db.query(Contact).get(contact_id)
    if not contact:
        return None

    update_data = data.dict(exclude_unset=True)
    old_values = {}

    for field, value in update_data.items():
        if hasattr(contact, field):
            old_values[field] = getattr(contact, field)
            setattr(contact, field, value)

    contact.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(contact)

    # Create activity record
    changes = [f"{k}: {old_values[k]} → {v}" for k, v in update_data.items()]
    create_contact_activity(
        db, contact.id, "contact.updated",
        f"Contact updated: {', '.join(changes)}"
    )

    return contact

def delete_contact(db: Session, contact_id: int) -> bool:
    contact = db.query(Contact).get(contact_id)
    if not contact:
        return False
    db.delete(contact)
    db.commit()
    return True

# Contact Notes

def create_contact_note(db: Session, data: ContactNoteCreate) -> ContactNote:
    obj = ContactNote(**data.dict())
    db.add(obj)
    db.commit()
    db.refresh(obj)

    # Create activity record
    create_contact_activity(
        db, obj.contact_id, "note.created",
        f"Note added: {obj.content[:50]}{'...' if len(obj.content) > 50 else ''}"
    )

    return obj

def get_contact_notes(db: Session, contact_id: int) -> List[ContactNote]:
    return db.query(ContactNote).filter(ContactNote.contact_id == contact_id).order_by(ContactNote.created_at.desc()).all()

def delete_contact_note(db: Session, note_id: int) -> bool:
    note = db.query(ContactNote).get(note_id)
    if not note:
        return False

    contact_id = note.contact_id
    db.delete(note)
    db.commit()

    # Create activity record
    create_contact_activity(
        db, contact_id, "note.deleted",
        "Note was deleted"
    )

    return True

# Contact Activities

def create_contact_activity(db: Session, contact_id: int, activity_type: str, description: str, activity_data: dict = None) -> ContactActivity:
    obj = ContactActivity(
        contact_id=contact_id,
        activity_type=activity_type,
        description=description,
        activity_data=activity_data or {}
    )
    db.add(obj)
    db.commit()
    db.refresh(obj)
    return obj

def get_contact_activities(db: Session, contact_id: int, limit: int = 50) -> List[ContactActivity]:
    return db.query(ContactActivity).filter(
        ContactActivity.contact_id == contact_id
    ).order_by(ContactActivity.created_at.desc()).limit(limit).all()

# Accounts

def create_account(db: Session, data: AccountCreate) -> Account:
    obj = Account(**data.dict())
    db.add(obj)
    db.commit()
    db.refresh(obj)
    return obj

def get_accounts(db: Session) -> List[Account]:
    return db.query(Account).all()

def get_account(db: Session, account_id: int) -> Optional[Account]:
    return db.query(Account).get(account_id)

# Leads

def create_lead(db: Session, data: LeadCreate) -> Lead:
    lead_data = data.dict()
    obj = Lead(**lead_data)
    db.add(obj)
    db.commit()
    db.refresh(obj)

    # Create activity record
    create_lead_activity(
        db, obj.id, "lead.created",
        f"Lead '{obj.title}' was created in {obj.stage} stage"
    )

    return obj

def get_leads(db: Session) -> List[Lead]:
    return db.query(Lead).all()

def get_lead(db: Session, lead_id: int) -> Optional[Lead]:
    return db.query(Lead).get(lead_id)

def update_lead(db: Session, lead_id: int, data: LeadUpdate) -> Optional[Lead]:
    lead = db.query(Lead).get(lead_id)
    if not lead:
        return None

    update_data = data.dict(exclude_unset=True)
    old_values = {}

    for field, value in update_data.items():
        if hasattr(lead, field):
            old_values[field] = getattr(lead, field)
            setattr(lead, field, value)

    lead.updated_at = datetime.utcnow()

    # Handle stage changes
    if 'stage' in update_data:
        if update_data['stage'] in ['closed_won', 'closed_lost']:
            lead.closed_at = datetime.utcnow()
        elif old_values.get('stage') in ['closed_won', 'closed_lost']:
            lead.closed_at = None

    db.commit()
    db.refresh(lead)

    # Create activity record
    changes = [f"{k}: {old_values[k]} → {v}" for k, v in update_data.items()]
    create_lead_activity(
        db, lead.id, "lead.updated",
        f"Lead updated: {', '.join(changes)}"
    )

    return lead

def delete_lead(db: Session, lead_id: int) -> bool:
    lead = db.query(Lead).get(lead_id)
    if not lead:
        return False
    db.delete(lead)
    db.commit()
    return True

def get_leads_by_stage(db: Session, stage: str) -> List[Lead]:
    return db.query(Lead).filter(Lead.stage == stage).all()

def get_pipeline_summary(db: Session) -> Dict[str, Any]:
    """Get pipeline summary with counts and values by stage"""
    stages = ['prospect', 'qualified', 'proposal', 'negotiation', 'closed_won', 'closed_lost']
    summary = {}

    for stage in stages:
        leads = db.query(Lead).filter(Lead.stage == stage).all()
        summary[stage] = {
            'count': len(leads),
            'total_value': sum(lead.value for lead in leads),
            'avg_probability': sum(lead.probability for lead in leads) / len(leads) if leads else 0
        }

    return summary

# Lead Notes

def create_lead_note(db: Session, data: LeadNoteCreate) -> LeadNote:
    obj = LeadNote(**data.dict())
    db.add(obj)
    db.commit()
    db.refresh(obj)

    # Create activity record
    create_lead_activity(
        db, obj.lead_id, "note.created",
        f"Note added: {obj.content[:50]}{'...' if len(obj.content) > 50 else ''}"
    )

    return obj

def get_lead_notes(db: Session, lead_id: int) -> List[LeadNote]:
    return db.query(LeadNote).filter(LeadNote.lead_id == lead_id).order_by(LeadNote.created_at.desc()).all()

def delete_lead_note(db: Session, note_id: int) -> bool:
    note = db.query(LeadNote).get(note_id)
    if not note:
        return False

    lead_id = note.lead_id
    db.delete(note)
    db.commit()

    # Create activity record
    create_lead_activity(
        db, lead_id, "note.deleted",
        "Note was deleted"
    )

    return True

# Lead Activities

def create_lead_activity(db: Session, lead_id: int, activity_type: str, description: str, activity_data: dict = None) -> LeadActivity:
    obj = LeadActivity(
        lead_id=lead_id,
        activity_type=activity_type,
        description=description,
        activity_data=activity_data or {}
    )
    db.add(obj)
    db.commit()
    db.refresh(obj)
    return obj

def get_lead_activities(db: Session, lead_id: int, limit: int = 50) -> List[LeadActivity]:
    return db.query(LeadActivity).filter(
        LeadActivity.lead_id == lead_id
    ).order_by(LeadActivity.created_at.desc()).limit(limit).all()

# Tasks

def create_task(db: Session, data: TaskCreate) -> Task:
    obj = Task(**data.dict())
    db.add(obj)
    db.commit()
    db.refresh(obj)
    return obj

def get_tasks(db: Session) -> List[Task]:
    return db.query(Task).all()

def update_task(db: Session, task_id: int) -> Optional[Task]:
    task = db.query(Task).get(task_id)
    if not task:
        return None
    task.completed = True
    db.commit()
    db.refresh(task)
    return task

def delete_task(db: Session, task_id: int) -> bool:
    task = db.query(Task).get(task_id)
    if not task:
        return False
    db.delete(task)
    db.commit()
    return True

# Webhooks

def create_webhook(db: Session, data: WebhookCreate) -> Webhook:
    obj = Webhook(**data.dict())
    db.add(obj)
    db.commit()
    db.refresh(obj)
    return obj

def get_webhooks(db: Session) -> List[Webhook]:
    return db.query(Webhook).all()

def get_webhook(db: Session, webhook_id: int) -> Optional[Webhook]:
    return db.query(Webhook).get(webhook_id)

def update_webhook(db: Session, webhook_id: int, data: WebhookUpdate) -> Optional[Webhook]:
    webhook = db.query(Webhook).get(webhook_id)
    if not webhook:
        return None

    update_data = data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(webhook, field, value)

    db.commit()
    db.refresh(webhook)
    return webhook

def delete_webhook(db: Session, webhook_id: int) -> bool:
    webhook = db.query(Webhook).get(webhook_id)
    if not webhook:
        return False
    db.delete(webhook)
    db.commit()
    return True

def create_webhook_log(db: Session, webhook_id: int, event_type: str, payload: dict,
                      response_status: Optional[int] = None, response_body: Optional[str] = None) -> WebhookLog:
    log = WebhookLog(
        webhook_id=webhook_id,
        event_type=event_type,
        payload=payload,
        response_status=response_status,
        response_body=response_body
    )
    db.add(log)
    db.commit()
    db.refresh(log)
    return log

def get_webhook_logs(db: Session, webhook_id: Optional[int] = None) -> List[WebhookLog]:
    query = db.query(WebhookLog)
    if webhook_id:
        query = query.filter(WebhookLog.webhook_id == webhook_id)
    return query.order_by(WebhookLog.created_at.desc()).all()
