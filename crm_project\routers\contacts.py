from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session
from crm_project.database import get_session
from crm_project.crud import create_contact, get_contacts, get_contact
from crm_project.schemas import <PERSON><PERSON><PERSON>, ContactRead

router = APIRouter(prefix="/contacts", tags=["Contacts"])

@router.post("/", response_model=ContactRead)
def add_contact(payload: ContactCreate, db: Session = Depends(get_session)):
    try:
        return create_contact(db, payload)
    except IntegrityError:
        raise HTTPException(status_code=400, detail="Email already exists")

@router.get("/", response_model=list[ContactRead])
def list_contacts(db: Session = Depends(get_session)):
    return get_contacts(db)

@router.get("/{contact_id}", response_model=ContactRead)
def get_one(contact_id: int, db: Session = Depends(get_session)):
    contact = get_contact(db, contact_id)
    if not contact:
        raise HTTPException(status_code=404, detail="Contact not found")
    return contact

@router.delete("/{contact_id}", response_model=bool)
def delete_contact(contact_id: int, db: Session = Depends(get_session)):
    from crm_project.crud import delete_contact as crud_delete_contact
    success = crud_delete_contact(db, contact_id)
    if not success:
        raise HTTPException(status_code=404, detail="Contact not found")
    return success
