from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, BackgroundTasks
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session
from crm_project.database import get_session
from crm_project.crud import create_contact, get_contacts, get_contact
from crm_project.schemas import <PERSON><PERSON><PERSON>, ContactRead
from crm_project.webhook_service import WebhookEvents, trigger_webhook_event

router = APIRouter(prefix="/contacts", tags=["Contacts"])

@router.post("/", response_model=ContactRead)
async def add_contact(
    payload: ContactCreate,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_session)
):
    try:
        contact = create_contact(db, payload)

        # Trigger webhook event
        contact_data = {
            "id": contact.id,
            "first_name": contact.first_name,
            "last_name": contact.last_name,
            "email": contact.email,
            "phone": contact.phone,
            "created_at": contact.created_at.isoformat()
        }
        background_tasks.add_task(trigger_webhook_event, WebhookEvents.CONTACT_CREATED, contact_data)

        return contact
    except IntegrityError:
        raise HTTPException(status_code=400, detail="Email already exists")

@router.get("/", response_model=list[ContactRead])
def list_contacts(db: Session = Depends(get_session)):
    return get_contacts(db)

@router.get("/{contact_id}", response_model=ContactRead)
def get_one(contact_id: int, db: Session = Depends(get_session)):
    contact = get_contact(db, contact_id)
    if not contact:
        raise HTTPException(status_code=404, detail="Contact not found")
    return contact

@router.delete("/{contact_id}", response_model=bool)
async def delete_contact(
    contact_id: int,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_session)
):
    # Get contact data before deletion for webhook
    contact = get_contact(db, contact_id)
    if not contact:
        raise HTTPException(status_code=404, detail="Contact not found")

    contact_data = {
        "id": contact.id,
        "first_name": contact.first_name,
        "last_name": contact.last_name,
        "email": contact.email,
        "phone": contact.phone
    }

    from crm_project.crud import delete_contact as crud_delete_contact
    success = crud_delete_contact(db, contact_id)

    if success:
        # Trigger webhook event
        background_tasks.add_task(trigger_webhook_event, WebhookEvents.CONTACT_DELETED, contact_data)

    return success
