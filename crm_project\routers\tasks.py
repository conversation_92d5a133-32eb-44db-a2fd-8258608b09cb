from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from crm_project.database import get_session
from crm_project.crud import create_task, get_tasks, update_task, delete_task
from crm_project.schemas import TaskCreate, TaskRead

router = APIRouter(prefix="/tasks", tags=["Tasks"])

@router.post("/", response_model=TaskRead)
def add_task(payload: TaskCreate, db: Session = Depends(get_session)):
    return create_task(db, payload)

@router.get("/", response_model=list[TaskRead])
def list_tasks(db: Session = Depends(get_session)):
    return get_tasks(db)

@router.put("/{task_id}/complete", response_model=TaskRead)
def complete_task(task_id: int, db: Session = Depends(get_session)):
    task = update_task(db, task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    return task

@router.delete("/{task_id}", response_model=bool)
def remove_task(task_id: int, db: Session = Depends(get_session)):
    return delete_task(db, task_id)
