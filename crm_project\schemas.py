from datetime import datetime
from typing import Optional
from pydantic import BaseModel

class ContactBase(BaseModel):
    first_name: str
    last_name: str
    email: str
    phone: Optional[str]

class ContactCreate(ContactBase):
    pass

class ContactRead(ContactBase):
    id: int
    created_at: datetime
    class Config:
        from_attributes = True

class AccountBase(BaseModel):
    name: str
    industry: Optional[str]
    website: Optional[str]

class AccountCreate(AccountBase):
    pass

class AccountRead(AccountBase):
    id: int
    created_at: datetime
    class Config:
        from_attributes = True

class LeadBase(BaseModel):
    contact_id: int
    status: Optional[str] = "new"
    source: Optional[str]

class LeadCreate(LeadBase):
    pass

class LeadRead(LeadBase):
    id: int
    created_at: datetime
    class Config:
        from_attributes = True

class TaskBase(BaseModel):
    description: str
    due_date: Optional[datetime]
    contact_id: Optional[int]

class TaskCreate(TaskBase):
    pass

class TaskRead(TaskBase):
    id: int
    completed: bool
    created_at: datetime
    class Config:
        from_attributes = True
