from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, HttpUrl

class ContactBase(BaseModel):
    first_name: str
    last_name: str
    email: str
    phone: Optional[str]

class ContactCreate(ContactBase):
    pass

class ContactRead(ContactBase):
    id: int
    created_at: datetime
    class Config:
        from_attributes = True

class AccountBase(BaseModel):
    name: str
    industry: Optional[str]
    website: Optional[str]

class AccountCreate(AccountBase):
    pass

class AccountRead(AccountBase):
    id: int
    created_at: datetime
    class Config:
        from_attributes = True

class LeadBase(BaseModel):
    contact_id: int
    status: Optional[str] = "new"
    source: Optional[str]

class LeadCreate(LeadBase):
    pass

class LeadRead(LeadBase):
    id: int
    created_at: datetime
    class Config:
        from_attributes = True

class TaskBase(BaseModel):
    description: str
    due_date: Optional[datetime]
    contact_id: Optional[int]

class TaskCreate(TaskBase):
    pass

class TaskRead(TaskBase):
    id: int
    completed: bool
    created_at: datetime
    class Config:
        from_attributes = True

# Webhook Schemas
class WebhookBase(BaseModel):
    name: str
    url: HttpUrl
    events: List[str]
    secret: Optional[str] = None
    active: bool = True

class WebhookCreate(WebhookBase):
    pass

class WebhookRead(WebhookBase):
    id: int
    created_at: datetime
    last_triggered: Optional[datetime] = None
    class Config:
        from_attributes = True

class WebhookUpdate(BaseModel):
    name: Optional[str] = None
    url: Optional[HttpUrl] = None
    events: Optional[List[str]] = None
    secret: Optional[str] = None
    active: Optional[bool] = None

# Webhook Log Schemas
class WebhookLogRead(BaseModel):
    id: int
    webhook_id: int
    event_type: str
    payload: Dict[str, Any]
    response_status: Optional[int] = None
    response_body: Optional[str] = None
    created_at: datetime
    class Config:
        from_attributes = True

# Event Schemas
class WebhookEvent(BaseModel):
    event_type: str
    data: Dict[str, Any]
    timestamp: datetime
