from fastapi import <PERSON><PERSON><PERSON>
from fastapi.responses import HTMLResponse
import uvicorn
from crm_project.database import init_db
from crm_project.routers import contacts, accounts, leads, tasks

app = FastAPI(title="PydanticAI CRM")

@app.on_event("startup")
def on_startup():
    init_db()

@app.get("/", response_class=HTMLResponse)
def root():
    return """
    <html>
        <head><title>PydanticAI CRM</title></head>
        <body style='font-family:sans-serif;text-align:center;margin-top:10vh;'>
            <h1>Welcome to PydanticAI CRM 🚀</h1>
            <p>Visit the <a href='/docs'>API Docs</a> to use the CRM endpoints.</p>
            <p>Or try <a href='/redoc'>ReDoc</a> for an alternative view.</p>
            <p>Or try the <a href='/app'>Simple CRM Web App</a> to interact with your data.</p>
        </body>
    </html>
    """

@app.get("/app", response_class=HTMLResponse)
def crm_app():
    return '''
    <html>
    <head>
        <title>Simple CRM Web App</title>
        <style>
            body { font-family: sans-serif; margin: 2em; }
            input, button { margin: 0.2em; }
            table { border-collapse: collapse; margin-top: 1em; }
            th, td { border: 1px solid #ccc; padding: 0.5em; }
        </style>
    </head>
    <body>
        <h2>Contacts</h2>
        <form id="addContactForm">
            <input type="text" id="first_name" placeholder="First Name" required>
            <input type="text" id="last_name" placeholder="Last Name" required>
            <input type="email" id="email" placeholder="Email" required>
            <input type="text" id="phone" placeholder="Phone">
            <button type="submit">Add Contact</button>
        </form>
        <table id="contactsTable">
            <thead>
                <tr><th>ID</th><th>Name</th><th>Email</th><th>Phone</th><th>Created</th><th>Delete</th></tr>
            </thead>
            <tbody></tbody>
        </table>
        <script>
        async function fetchContacts() {
            const res = await fetch('/contacts');
            const data = await res.json();
            const tbody = document.querySelector('#contactsTable tbody');
            tbody.innerHTML = '';
            data.forEach(c => {
                const tr = document.createElement('tr');
                tr.innerHTML = `<td>${c.id}</td><td>${c.first_name} ${c.last_name}</td><td>${c.email}</td><td>${c.phone||''}</td><td>${c.created_at.split('T')[0]}</td><td><button onclick="deleteContact(${c.id})">Delete</button></td>`;
                tbody.appendChild(tr);
            });
        }
        async function deleteContact(id) {
            await fetch(`/contacts/${id}`, { method: 'DELETE' });
            fetchContacts();
        }
        document.getElementById('addContactForm').onsubmit = async (e) => {
            e.preventDefault();
            const payload = {
                first_name: document.getElementById('first_name').value,
                last_name: document.getElementById('last_name').value,
                email: document.getElementById('email').value,
                phone: document.getElementById('phone').value
            };
            await fetch('/contacts/', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
            });
            e.target.reset();
            fetchContacts();
        };
        fetchContacts();
        </script>
    </body>
    </html>
    '''

app.include_router(contacts.router)
app.include_router(accounts.router)
app.include_router(leads.router)
app.include_router(tasks.router)

if __name__ == "__main__":
    uvicorn.run("crm_project.main:app", host="127.0.0.1", port=8000, reload=True)
