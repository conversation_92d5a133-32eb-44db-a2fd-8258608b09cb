from fastapi import <PERSON><PERSON><PERSON>
from fastapi.responses import HTMLResponse
import uvicorn
from crm_project.database import init_db
from crm_project.routers import contacts, accounts, leads, tasks, webhooks

app = FastAPI(title="PydanticAI CRM")

@app.on_event("startup")
def on_startup():
    init_db()

@app.get("/", response_class=HTMLResponse)
def root():
    return """
    <html>
        <head><title>PydanticAI CRM</title></head>
        <body style='font-family:sans-serif;text-align:center;margin-top:10vh;'>
            <h1>Welcome to PydanticAI CRM 🚀</h1>
            <p>Visit the <a href='/docs'>API Docs</a> to use the CRM endpoints.</p>
            <p>Or try <a href='/redoc'>ReDoc</a> for an alternative view.</p>
            <p>Or try the <a href='/app'>Simple CRM Web App</a> to interact with your data.</p>
            <p>Or use the <a href='/contacts-enhanced'>Enhanced Contact Manager</a> with notes and activities.</p>
            <p>Or manage <a href='/webhooks-ui'>Webhooks</a> for integrations.</p>
        </body>
    </html>
    """

@app.get("/app", response_class=HTMLResponse)
def crm_app():
    return '''
    <html>
    <head>
        <title>Simple CRM Web App</title>
        <style>
            body { font-family: sans-serif; margin: 2em; background-color: #f5f5f5; }
            input, button { margin: 0.2em; padding: 0.5em; }
            button { background-color: #007bff; color: white; border: none; cursor: pointer; }
            button:hover { background-color: #0056b3; }
            table { border-collapse: collapse; margin-top: 1em; width: 100%; background-color: white; }
            th, td { border: 1px solid #ccc; padding: 0.5em; text-align: left; }
            th { background-color: #f8f9fa; }
            .loading { color: #666; font-style: italic; }
            .error { color: #dc3545; }
        </style>
    </head>
    <body>
        <h2>Contacts</h2>
        <form id="addContactForm">
            <input type="text" id="first_name" placeholder="First Name" required>
            <input type="text" id="last_name" placeholder="Last Name" required>
            <input type="email" id="email" placeholder="Email" required>
            <input type="text" id="phone" placeholder="Phone">
            <button type="submit">Add Contact</button>
        </form>
        <table id="contactsTable">
            <thead>
                <tr><th>ID</th><th>Name</th><th>Email</th><th>Phone</th><th>Created</th><th>Delete</th></tr>
            </thead>
            <tbody></tbody>
        </table>
        <script>
        async function fetchContacts() {
            try {
                const tbody = document.querySelector('#contactsTable tbody');
                tbody.innerHTML = '<tr><td colspan="6" class="loading">Loading contacts...</td></tr>';

                const res = await fetch('/contacts');
                if (!res.ok) {
                    throw new Error(`HTTP error! status: ${res.status}`);
                }
                const data = await res.json();

                tbody.innerHTML = '';
                if (data.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="6">No contacts found</td></tr>';
                    return;
                }

                data.forEach(c => {
                    const tr = document.createElement('tr');
                    tr.innerHTML = `<td>${c.id}</td><td>${c.first_name} ${c.last_name}</td><td>${c.email}</td><td>${c.phone||''}</td><td>${c.created_at.split('T')[0]}</td><td><button onclick="deleteContact(${c.id})">Delete</button></td>`;
                    tbody.appendChild(tr);
                });
            } catch (error) {
                console.error('Error fetching contacts:', error);
                const tbody = document.querySelector('#contactsTable tbody');
                tbody.innerHTML = '<tr><td colspan="6" class="error">Error loading contacts. Check console for details.</td></tr>';
            }
        }

        async function deleteContact(id) {
            try {
                const res = await fetch(`/contacts/${id}`, { method: 'DELETE' });
                if (!res.ok) {
                    throw new Error(`HTTP error! status: ${res.status}`);
                }
                fetchContacts();
            } catch (error) {
                console.error('Error deleting contact:', error);
                alert('Error deleting contact. Check console for details.');
            }
        }

        document.getElementById('addContactForm').onsubmit = async (e) => {
            e.preventDefault();
            try {
                const payload = {
                    first_name: document.getElementById('first_name').value,
                    last_name: document.getElementById('last_name').value,
                    email: document.getElementById('email').value,
                    phone: document.getElementById('phone').value
                };

                const res = await fetch('/contacts/', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(payload)
                });

                if (!res.ok) {
                    const errorData = await res.json();
                    throw new Error(errorData.detail || `HTTP error! status: ${res.status}`);
                }

                e.target.reset();
                fetchContacts();
            } catch (error) {
                console.error('Error adding contact:', error);
                alert('Error adding contact: ' + error.message);
            }
        };

        // Initialize the page
        fetchContacts();
        </script>
    </body>
    </html>
    '''

@app.get("/webhooks-ui", response_class=HTMLResponse)
def webhooks_ui():
    return '''
    <html>
    <head>
        <title>Webhook Management - CRM</title>
        <style>
            body { font-family: sans-serif; margin: 2em; background-color: #f5f5f5; }
            input, button, select, textarea { margin: 0.2em; padding: 0.5em; }
            button { background-color: #007bff; color: white; border: none; cursor: pointer; }
            button:hover { background-color: #0056b3; }
            button.danger { background-color: #dc3545; }
            button.danger:hover { background-color: #c82333; }
            button.success { background-color: #28a745; }
            button.success:hover { background-color: #218838; }
            table { border-collapse: collapse; margin-top: 1em; width: 100%; background-color: white; }
            th, td { border: 1px solid #ccc; padding: 0.5em; text-align: left; }
            th { background-color: #f8f9fa; }
            .form-group { margin: 1em 0; }
            .form-group label { display: block; margin-bottom: 0.5em; font-weight: bold; }
            .form-group input, .form-group select, .form-group textarea { width: 100%; max-width: 400px; }
            .checkbox-group { display: flex; flex-wrap: wrap; gap: 1em; }
            .checkbox-group label { display: flex; align-items: center; font-weight: normal; }
            .checkbox-group input { width: auto; margin-right: 0.5em; }
            .status-active { color: #28a745; font-weight: bold; }
            .status-inactive { color: #dc3545; font-weight: bold; }
            .nav { margin-bottom: 2em; }
            .nav a { margin-right: 1em; color: #007bff; text-decoration: none; }
            .nav a:hover { text-decoration: underline; }
        </style>
    </head>
    <body>
        <div class="nav">
            <a href="/">← Back to Home</a>
            <a href="/app">CRM App</a>
            <a href="/docs">API Docs</a>
        </div>

        <h1>Webhook Management</h1>

        <h2>Create New Webhook</h2>
        <form id="webhookForm">
            <div class="form-group">
                <label for="name">Name:</label>
                <input type="text" id="name" required placeholder="My Integration Webhook">
            </div>
            <div class="form-group">
                <label for="url">URL:</label>
                <input type="url" id="url" required placeholder="https://example.com/webhook">
            </div>
            <div class="form-group">
                <label for="secret">Secret (optional):</label>
                <input type="text" id="secret" placeholder="webhook_secret_key">
            </div>
            <div class="form-group">
                <label>Events to listen for:</label>
                <div class="checkbox-group" id="eventsGroup">
                    <!-- Events will be loaded dynamically -->
                </div>
            </div>
            <button type="submit">Create Webhook</button>
        </form>

        <h2>Existing Webhooks</h2>
        <table id="webhooksTable">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>URL</th>
                    <th>Events</th>
                    <th>Status</th>
                    <th>Last Triggered</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody></tbody>
        </table>

        <script>
        let availableEvents = [];

        async function loadAvailableEvents() {
            try {
                const res = await fetch('/webhooks/events/available');
                const data = await res.json();
                availableEvents = data.events;

                const eventsGroup = document.getElementById('eventsGroup');
                eventsGroup.innerHTML = '';

                availableEvents.forEach(event => {
                    const label = document.createElement('label');
                    label.innerHTML = `
                        <input type="checkbox" name="events" value="${event.name}">
                        ${event.name}
                    `;
                    eventsGroup.appendChild(label);
                });
            } catch (error) {
                console.error('Error loading events:', error);
            }
        }

        async function loadWebhooks() {
            try {
                const res = await fetch('/webhooks/');
                const webhooks = await res.json();

                const tbody = document.querySelector('#webhooksTable tbody');
                tbody.innerHTML = '';

                if (webhooks.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="7">No webhooks configured</td></tr>';
                    return;
                }

                webhooks.forEach(webhook => {
                    const tr = document.createElement('tr');
                    const lastTriggered = webhook.last_triggered ?
                        new Date(webhook.last_triggered).toLocaleString() : 'Never';
                    const status = webhook.active ?
                        '<span class="status-active">Active</span>' :
                        '<span class="status-inactive">Inactive</span>';

                    tr.innerHTML = `
                        <td>${webhook.id}</td>
                        <td>${webhook.name}</td>
                        <td>${webhook.url}</td>
                        <td>${webhook.events.join(', ')}</td>
                        <td>${status}</td>
                        <td>${lastTriggered}</td>
                        <td>
                            <button class="success" onclick="testWebhook(${webhook.id})">Test</button>
                            <button onclick="toggleWebhook(${webhook.id}, ${!webhook.active})">${webhook.active ? 'Disable' : 'Enable'}</button>
                            <button class="danger" onclick="deleteWebhook(${webhook.id})">Delete</button>
                        </td>
                    `;
                    tbody.appendChild(tr);
                });
            } catch (error) {
                console.error('Error loading webhooks:', error);
            }
        }

        async function testWebhook(id) {
            try {
                const res = await fetch(`/webhooks/test/${id}`, { method: 'POST' });
                if (res.ok) {
                    alert('Test webhook sent successfully!');
                } else {
                    alert('Failed to send test webhook');
                }
            } catch (error) {
                console.error('Error testing webhook:', error);
                alert('Error testing webhook');
            }
        }

        async function toggleWebhook(id, active) {
            try {
                const res = await fetch(`/webhooks/${id}`, {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ active })
                });
                if (res.ok) {
                    loadWebhooks();
                } else {
                    alert('Failed to update webhook');
                }
            } catch (error) {
                console.error('Error updating webhook:', error);
                alert('Error updating webhook');
            }
        }

        async function deleteWebhook(id) {
            if (!confirm('Are you sure you want to delete this webhook?')) return;

            try {
                const res = await fetch(`/webhooks/${id}`, { method: 'DELETE' });
                if (res.ok) {
                    loadWebhooks();
                } else {
                    alert('Failed to delete webhook');
                }
            } catch (error) {
                console.error('Error deleting webhook:', error);
                alert('Error deleting webhook');
            }
        }

        document.getElementById('webhookForm').onsubmit = async (e) => {
            e.preventDefault();

            const selectedEvents = Array.from(document.querySelectorAll('input[name="events"]:checked'))
                .map(cb => cb.value);

            if (selectedEvents.length === 0) {
                alert('Please select at least one event');
                return;
            }

            const payload = {
                name: document.getElementById('name').value,
                url: document.getElementById('url').value,
                secret: document.getElementById('secret').value || null,
                events: selectedEvents,
                active: true
            };

            try {
                const res = await fetch('/webhooks/', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(payload)
                });

                if (res.ok) {
                    e.target.reset();
                    document.querySelectorAll('input[name="events"]').forEach(cb => cb.checked = false);
                    loadWebhooks();
                    alert('Webhook created successfully!');
                } else {
                    const error = await res.json();
                    alert('Error creating webhook: ' + (error.detail || 'Unknown error'));
                }
            } catch (error) {
                console.error('Error creating webhook:', error);
                alert('Error creating webhook');
            }
        };

        // Initialize
        loadAvailableEvents();
        loadWebhooks();
        </script>
    </body>
    </html>
    '''

@app.get("/leads-pipeline", response_class=HTMLResponse)
def leads_pipeline_ui():
    try:
        with open("crm_project/templates/leads_pipeline.html", "r", encoding="utf-8") as f:
            return f.read()
    except (FileNotFoundError, UnicodeDecodeError):
        return lead_pipeline_fallback_html()

@app.get("/contacts-enhanced", response_class=HTMLResponse)
def enhanced_contacts_ui():
    try:
        with open("crm_project/templates/enhanced_contacts.html", "r", encoding="utf-8") as f:
            return f.read()
    except (FileNotFoundError, UnicodeDecodeError):
        # Fallback HTML if template file is not found
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Enhanced Contact Management - CRM</title>
            <style>
                body { font-family: sans-serif; margin: 20px; background-color: #f8f9fa; }
                .container { max-width: 1200px; margin: 0 auto; }
                .card { background: white; border-radius: 8px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                .nav a { margin-right: 15px; color: #007bff; text-decoration: none; }
                .nav a:hover { text-decoration: underline; }
                .btn { background-color: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin: 5px; }
                .btn:hover { background-color: #0056b3; }
                .btn-danger { background-color: #dc3545; }
                .btn-danger:hover { background-color: #c82333; }
                .contact-item { padding: 15px; border: 1px solid #e9ecef; border-radius: 6px; margin-bottom: 10px; cursor: pointer; }
                .contact-item:hover { background-color: #f8f9fa; }
                .contact-item.selected { background-color: #e3f2fd; border-color: #007bff; }
                .form-group { margin-bottom: 15px; }
                .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
                .form-group input, .form-group select, .form-group textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
                .main-content { display: flex; gap: 20px; }
                .contacts-list { flex: 1; }
                .contact-detail { flex: 1; }
                .hidden { display: none; }
                .modal { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; }
                .modal-content { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 20px; border-radius: 8px; width: 90%; max-width: 500px; }
                .close { float: right; font-size: 24px; cursor: pointer; }
                .activity-item { padding: 10px; border-left: 3px solid #007bff; margin-bottom: 10px; background: #f8f9fa; }
                .note-item { padding: 10px; border: 1px solid #e9ecef; border-radius: 4px; margin-bottom: 10px; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="card">
                    <div class="nav">
                        <a href="/">← Back to Home</a>
                        <a href="/app">Simple CRM</a>
                        <a href="/docs">API Docs</a>
                        <a href="/webhooks-ui">Webhooks</a>
                    </div>
                    <h1>Enhanced Contact Management</h1>
                    <button class="btn" onclick="showCreateModal()">+ Add New Contact</button>
                </div>

                <div class="main-content">
                    <div class="contacts-list">
                        <div class="card">
                            <h2>Contacts</h2>
                            <input type="text" id="searchInput" placeholder="Search contacts..." style="width: 100%; padding: 8px; margin-bottom: 15px; border: 1px solid #ddd; border-radius: 4px;">
                            <div id="contactsList"></div>
                        </div>
                    </div>

                    <div class="contact-detail">
                        <div class="card">
                            <div id="contactDetail">
                                <p>Select a contact to view details</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Create Contact Modal -->
            <div id="createModal" class="modal hidden">
                <div class="modal-content">
                    <span class="close" onclick="hideCreateModal()">&times;</span>
                    <h2>Create New Contact</h2>
                    <form id="createContactForm">
                        <div class="form-group">
                            <label>First Name *</label>
                            <input type="text" name="first_name" required>
                        </div>
                        <div class="form-group">
                            <label>Last Name *</label>
                            <input type="text" name="last_name" required>
                        </div>
                        <div class="form-group">
                            <label>Email *</label>
                            <input type="email" name="email" required>
                        </div>
                        <div class="form-group">
                            <label>Phone</label>
                            <input type="text" name="phone">
                        </div>
                        <div class="form-group">
                            <label>Job Title</label>
                            <input type="text" name="job_title">
                        </div>
                        <div class="form-group">
                            <label>Department</label>
                            <input type="text" name="department">
                        </div>
                        <div class="form-group">
                            <label>Status</label>
                            <select name="status">
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                                <option value="prospect">Prospect</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Lead Source</label>
                            <input type="text" name="lead_source" placeholder="e.g., website, referral, cold call">
                        </div>
                        <button type="submit" class="btn">Create Contact</button>
                        <button type="button" class="btn btn-danger" onclick="hideCreateModal()">Cancel</button>
                    </form>
                </div>
            </div>

            <script>
                let contacts = [];
                let selectedContactId = null;

                async function loadContacts() {
                    try {
                        const response = await fetch('/contacts/');
                        contacts = await response.json();
                        displayContacts();
                    } catch (error) {
                        console.error('Error loading contacts:', error);
                        document.getElementById('contactsList').innerHTML = '<p>Error loading contacts</p>';
                    }
                }

                function displayContacts() {
                    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
                    const filteredContacts = contacts.filter(contact =>
                        contact.first_name.toLowerCase().includes(searchTerm) ||
                        contact.last_name.toLowerCase().includes(searchTerm) ||
                        contact.email.toLowerCase().includes(searchTerm)
                    );

                    const html = filteredContacts.map(contact => `
                        <div class="contact-item ${selectedContactId === contact.id ? 'selected' : ''}"
                             onclick="selectContact(${contact.id})">
                            <strong>${contact.first_name} ${contact.last_name}</strong><br>
                            <small>${contact.email}</small><br>
                            <small>${contact.job_title || ''} ${contact.department ? '• ' + contact.department : ''}</small>
                        </div>
                    `).join('');

                    document.getElementById('contactsList').innerHTML = html || '<p>No contacts found</p>';
                }

                async function selectContact(contactId) {
                    selectedContactId = contactId;
                    displayContacts();

                    try {
                        const response = await fetch(`/contacts/${contactId}`);
                        const contact = await response.json();
                        displayContactDetail(contact);
                    } catch (error) {
                        console.error('Error loading contact details:', error);
                        document.getElementById('contactDetail').innerHTML = '<p>Error loading contact details</p>';
                    }
                }

                function displayContactDetail(contact) {
                    const html = `
                        <h2>${contact.first_name} ${contact.last_name}</h2>
                        <p><strong>Email:</strong> ${contact.email}</p>
                        <p><strong>Phone:</strong> ${contact.phone || 'N/A'}</p>
                        <p><strong>Job Title:</strong> ${contact.job_title || 'N/A'}</p>
                        <p><strong>Department:</strong> ${contact.department || 'N/A'}</p>
                        <p><strong>Status:</strong> ${contact.status || 'N/A'}</p>
                        <p><strong>Lead Source:</strong> ${contact.lead_source || 'N/A'}</p>
                        <p><strong>Created:</strong> ${new Date(contact.created_at).toLocaleDateString()}</p>

                        <h3>Notes (${contact.notes ? contact.notes.length : 0})</h3>
                        <div id="notesList">
                            ${contact.notes ? contact.notes.map(note => `
                                <div class="note-item">
                                    <p>${note.content}</p>
                                    <small>Created: ${new Date(note.created_at).toLocaleString()}</small>
                                </div>
                            `).join('') : '<p>No notes</p>'}
                        </div>

                        <h3>Activities (${contact.activities ? contact.activities.length : 0})</h3>
                        <div id="activitiesList">
                            ${contact.activities ? contact.activities.map(activity => `
                                <div class="activity-item">
                                    <strong>${activity.activity_type}</strong>: ${activity.description}
                                    <br><small>${new Date(activity.created_at).toLocaleString()}</small>
                                </div>
                            `).join('') : '<p>No activities</p>'}
                        </div>
                    `;
                    document.getElementById('contactDetail').innerHTML = html;
                }

                function showCreateModal() {
                    document.getElementById('createModal').classList.remove('hidden');
                }

                function hideCreateModal() {
                    document.getElementById('createModal').classList.add('hidden');
                    document.getElementById('createContactForm').reset();
                }

                document.getElementById('createContactForm').onsubmit = async (e) => {
                    e.preventDefault();
                    const formData = new FormData(e.target);
                    const data = Object.fromEntries(formData.entries());

                    try {
                        const response = await fetch('/contacts/', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify(data)
                        });

                        if (response.ok) {
                            hideCreateModal();
                            loadContacts();
                        } else {
                            alert('Error creating contact');
                        }
                    } catch (error) {
                        console.error('Error creating contact:', error);
                        alert('Error creating contact');
                    }
                };

                document.getElementById('searchInput').oninput = displayContacts;

                // Initialize
                loadContacts();
            </script>
        </body>
        </html>
        """

def lead_pipeline_fallback_html():
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Lead Pipeline - CRM</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                padding: 20px;
            }
            .header {
                background: white;
                padding: 20px;
                border-radius: 12px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.1);
                margin-bottom: 30px;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            .header h1 {
                color: #2d3748;
                font-size: 28px;
                font-weight: 700;
            }
            .pipeline-stats {
                display: flex;
                gap: 20px;
                margin-bottom: 30px;
            }
            .stat-card {
                background: white;
                padding: 20px;
                border-radius: 12px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.1);
                flex: 1;
                text-align: center;
            }
            .stat-value {
                font-size: 32px;
                font-weight: 700;
                color: #667eea;
                margin-bottom: 5px;
            }
            .stat-label {
                color: #718096;
                font-size: 14px;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }
            .pipeline-container {
                display: flex;
                gap: 20px;
                overflow-x: auto;
                padding-bottom: 20px;
            }
            .pipeline-stage {
                background: white;
                border-radius: 12px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.1);
                min-width: 300px;
                max-width: 300px;
                padding: 20px;
                height: fit-content;
            }
            .stage-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 20px;
                padding-bottom: 15px;
                border-bottom: 2px solid #e2e8f0;
            }
            .stage-title {
                font-size: 18px;
                font-weight: 600;
                color: #2d3748;
            }
            .stage-count {
                background: #667eea;
                color: white;
                padding: 4px 12px;
                border-radius: 20px;
                font-size: 12px;
                font-weight: 600;
            }
            .lead-card {
                background: #f7fafc;
                border: 1px solid #e2e8f0;
                border-radius: 8px;
                padding: 16px;
                margin-bottom: 12px;
                cursor: pointer;
                transition: all 0.2s ease;
                border-left: 4px solid #667eea;
            }
            .lead-card:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            }
            .lead-title {
                font-weight: 600;
                color: #2d3748;
                margin-bottom: 8px;
                font-size: 16px;
            }
            .lead-contact {
                color: #718096;
                font-size: 14px;
                margin-bottom: 8px;
            }
            .lead-value {
                font-weight: 600;
                color: #38a169;
                font-size: 18px;
                margin-bottom: 8px;
            }
            .lead-meta {
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: 12px;
                color: #a0aec0;
            }
            .probability {
                background: #edf2f7;
                padding: 2px 8px;
                border-radius: 12px;
                font-weight: 500;
            }
            .btn {
                background: #667eea;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                cursor: pointer;
                font-weight: 600;
                transition: background 0.2s ease;
            }
            .btn:hover {
                background: #5a67d8;
            }
            .btn-secondary {
                background: #e2e8f0;
                color: #4a5568;
            }
            .btn-secondary:hover {
                background: #cbd5e0;
            }
            .modal {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                z-index: 1000;
            }
            .modal-content {
                background: white;
                margin: 5% auto;
                padding: 30px;
                border-radius: 12px;
                width: 90%;
                max-width: 600px;
                max-height: 80vh;
                overflow-y: auto;
            }
            .form-group {
                margin-bottom: 20px;
            }
            .form-group label {
                display: block;
                margin-bottom: 8px;
                font-weight: 600;
                color: #2d3748;
            }
            .form-group input, .form-group select, .form-group textarea {
                width: 100%;
                padding: 12px;
                border: 1px solid #e2e8f0;
                border-radius: 8px;
                font-size: 14px;
            }
            .form-group textarea {
                resize: vertical;
                min-height: 100px;
            }
            .form-row {
                display: flex;
                gap: 15px;
            }
            .form-row .form-group {
                flex: 1;
            }
            .loading {
                text-align: center;
                padding: 40px;
                color: #718096;
            }
            .empty-state {
                text-align: center;
                padding: 40px;
                color: #a0aec0;
                font-style: italic;
            }
            .priority-high { border-left-color: #e53e3e; }
            .priority-medium { border-left-color: #dd6b20; }
            .priority-low { border-left-color: #38a169; }
            .priority-urgent { border-left-color: #9f7aea; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🎯 Lead Pipeline</h1>
            <div>
                <button class="btn" onclick="openCreateModal()">+ New Lead</button>
                <button class="btn btn-secondary" onclick="window.location.href='/contacts-enhanced'">Contacts</button>
            </div>
        </div>

        <div class="pipeline-stats" id="pipelineStats">
            <div class="stat-card">
                <div class="stat-value" id="totalLeads">-</div>
                <div class="stat-label">Total Leads</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="totalValue">-</div>
                <div class="stat-label">Pipeline Value</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="avgProbability">-</div>
                <div class="stat-label">Avg Probability</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="closedWon">-</div>
                <div class="stat-label">Closed Won</div>
            </div>
        </div>

        <div class="pipeline-container" id="pipelineContainer">
            <div class="loading">Loading pipeline...</div>
        </div>

        <!-- Create Lead Modal -->
        <div id="createModal" class="modal">
            <div class="modal-content">
                <h2 style="margin-bottom: 20px;">Create New Lead</h2>
                <form id="createLeadForm">
                    <div class="form-group">
                        <label>Lead Title *</label>
                        <input type="text" id="title" required placeholder="e.g., Website Redesign Project">
                    </div>

                    <div class="form-group">
                        <label>Description</label>
                        <textarea id="description" placeholder="Lead description and details..."></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label>Contact *</label>
                            <select id="contact_id" required>
                                <option value="">Select Contact</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Account</label>
                            <select id="account_id">
                                <option value="">Select Account</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label>Stage</label>
                            <select id="stage">
                                <option value="prospect">Prospect</option>
                                <option value="qualified">Qualified</option>
                                <option value="proposal">Proposal</option>
                                <option value="negotiation">Negotiation</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Priority</label>
                            <select id="priority">
                                <option value="low">Low</option>
                                <option value="medium" selected>Medium</option>
                                <option value="high">High</option>
                                <option value="urgent">Urgent</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label>Deal Value ($)</label>
                            <input type="number" id="value" min="0" step="100" placeholder="0">
                        </div>
                        <div class="form-group">
                            <label>Probability (%)</label>
                            <input type="number" id="probability" min="0" max="100" value="10">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label>Source</label>
                            <input type="text" id="source" placeholder="e.g., Website, Referral, Cold Call">
                        </div>
                        <div class="form-group">
                            <label>Campaign</label>
                            <input type="text" id="campaign" placeholder="Marketing campaign">
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Expected Close Date</label>
                        <input type="date" id="expected_close_date">
                    </div>

                    <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 30px;">
                        <button type="button" class="btn btn-secondary" onclick="closeCreateModal()">Cancel</button>
                        <button type="submit" class="btn">Create Lead</button>
                    </div>
                </form>
            </div>
        </div>

        <script>
            let leads = [];
            let contacts = [];
            let accounts = [];

            const stages = [
                { key: 'prospect', title: 'Prospect', color: '#718096' },
                { key: 'qualified', title: 'Qualified', color: '#3182ce' },
                { key: 'proposal', title: 'Proposal', color: '#d69e2e' },
                { key: 'negotiation', title: 'Negotiation', color: '#dd6b20' },
                { key: 'closed_won', title: 'Closed Won', color: '#38a169' },
                { key: 'closed_lost', title: 'Closed Lost', color: '#e53e3e' }
            ];

            async function loadData() {
                try {
                    const [leadsRes, contactsRes, accountsRes] = await Promise.all([
                        fetch('/leads/'),
                        fetch('/contacts/'),
                        fetch('/accounts/')
                    ]);

                    leads = await leadsRes.json();
                    contacts = await contactsRes.json();
                    accounts = await accountsRes.json();

                    populateContactOptions();
                    populateAccountOptions();
                    renderPipeline();
                    updateStats();
                } catch (error) {
                    console.error('Error loading data:', error);
                    document.getElementById('pipelineContainer').innerHTML =
                        '<div class="loading">Error loading pipeline data</div>';
                }
            }

            function populateContactOptions() {
                const select = document.getElementById('contact_id');
                select.innerHTML = '<option value="">Select Contact</option>';
                contacts.forEach(contact => {
                    const option = document.createElement('option');
                    option.value = contact.id;
                    option.textContent = `${contact.first_name} ${contact.last_name} (${contact.email})`;
                    select.appendChild(option);
                });
            }

            function populateAccountOptions() {
                const select = document.getElementById('account_id');
                select.innerHTML = '<option value="">Select Account</option>';
                accounts.forEach(account => {
                    const option = document.createElement('option');
                    option.value = account.id;
                    option.textContent = account.name;
                    select.appendChild(option);
                });
            }

            function renderPipeline() {
                const container = document.getElementById('pipelineContainer');
                container.innerHTML = '';

                stages.forEach(stage => {
                    const stageLeads = leads.filter(lead => lead.stage === stage.key);
                    const stageElement = createStageElement(stage, stageLeads);
                    container.appendChild(stageElement);
                });
            }

            function createStageElement(stage, stageLeads) {
                const stageDiv = document.createElement('div');
                stageDiv.className = 'pipeline-stage';

                const totalValue = stageLeads.reduce((sum, lead) => sum + (lead.value || 0), 0);

                stageDiv.innerHTML = `
                    <div class="stage-header">
                        <div class="stage-title">${stage.title}</div>
                        <div class="stage-count">${stageLeads.length}</div>
                    </div>
                    <div style="margin-bottom: 15px; font-size: 14px; color: #718096;">
                        Total: $${(totalValue / 100).toLocaleString()}
                    </div>
                    <div class="stage-leads">
                        ${stageLeads.length === 0 ?
                            '<div class="empty-state">No leads in this stage</div>' :
                            stageLeads.map(lead => createLeadCard(lead)).join('')
                        }
                    </div>
                `;

                return stageDiv;
            }

            function createLeadCard(lead) {
                const contact = contacts.find(c => c.id === lead.contact_id);
                const account = accounts.find(a => a.id === lead.account_id);

                return `
                    <div class="lead-card priority-${lead.priority}" onclick="openLeadDetail(${lead.id})">
                        <div class="lead-title">${lead.title}</div>
                        <div class="lead-contact">
                            ${contact ? `${contact.first_name} ${contact.last_name}` : 'Unknown Contact'}
                            ${account ? ` • ${account.name}` : ''}
                        </div>
                        <div class="lead-value">$${((lead.value || 0) / 100).toLocaleString()}</div>
                        <div class="lead-meta">
                            <span class="probability">${lead.probability}%</span>
                            <span>${lead.priority.toUpperCase()}</span>
                        </div>
                    </div>
                `;
            }

            function updateStats() {
                const totalLeads = leads.length;
                const totalValue = leads.reduce((sum, lead) => sum + (lead.value || 0), 0);
                const avgProbability = totalLeads > 0 ?
                    Math.round(leads.reduce((sum, lead) => sum + lead.probability, 0) / totalLeads) : 0;
                const closedWon = leads.filter(lead => lead.stage === 'closed_won').length;

                document.getElementById('totalLeads').textContent = totalLeads;
                document.getElementById('totalValue').textContent = `$${(totalValue / 100).toLocaleString()}`;
                document.getElementById('avgProbability').textContent = `${avgProbability}%`;
                document.getElementById('closedWon').textContent = closedWon;
            }

            function openCreateModal() {
                document.getElementById('createModal').style.display = 'block';
            }

            function closeCreateModal() {
                document.getElementById('createModal').style.display = 'none';
                document.getElementById('createLeadForm').reset();
            }

            function openLeadDetail(leadId) {
                // For now, just show an alert. In a full implementation, this would open a detailed modal
                const lead = leads.find(l => l.id === leadId);
                alert(`Lead Details:\\n\\nTitle: ${lead.title}\\nStage: ${lead.stage}\\nValue: $${(lead.value / 100).toLocaleString()}\\nProbability: ${lead.probability}%`);
            }

            document.getElementById('createLeadForm').onsubmit = async (e) => {
                e.preventDefault();

                const formData = {
                    title: document.getElementById('title').value,
                    description: document.getElementById('description').value,
                    contact_id: parseInt(document.getElementById('contact_id').value),
                    account_id: document.getElementById('account_id').value ?
                        parseInt(document.getElementById('account_id').value) : null,
                    stage: document.getElementById('stage').value,
                    priority: document.getElementById('priority').value,
                    value: Math.round((parseFloat(document.getElementById('value').value) || 0) * 100),
                    probability: parseInt(document.getElementById('probability').value),
                    source: document.getElementById('source').value,
                    campaign: document.getElementById('campaign').value,
                    expected_close_date: document.getElementById('expected_close_date').value || null
                };

                try {
                    const response = await fetch('/leads/', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(formData)
                    });

                    if (response.ok) {
                        closeCreateModal();
                        loadData(); // Reload the pipeline
                    } else {
                        alert('Error creating lead');
                    }
                } catch (error) {
                    console.error('Error creating lead:', error);
                    alert('Error creating lead');
                }
            };

            // Close modal when clicking outside
            window.onclick = (event) => {
                const modal = document.getElementById('createModal');
                if (event.target === modal) {
                    closeCreateModal();
                }
            };

            // Initialize
            loadData();
        </script>
    </body>
    </html>
    """

app.include_router(contacts.router)
app.include_router(accounts.router)
app.include_router(leads.router)
app.include_router(tasks.router)
app.include_router(webhooks.router)

if __name__ == "__main__":
    uvicorn.run("crm_project.main:app", host="127.0.0.1", port=8000, reload=True)
